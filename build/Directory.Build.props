<Project>

  <!-- Common properties for all projects -->
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn> <!-- Missing XML comment for publicly visible type or member -->
  </PropertyGroup>

  <!-- Assembly metadata -->
  <PropertyGroup>
    <Company>Divide By Zero Studios</Company>
    <Product>SMS Gateway</Product>
    <Copyright>Copyright © Divide By Zero Studios 2024</Copyright>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
    <Authors>Michael</Authors>
  </PropertyGroup>

  <!-- Source control and repository information -->
  <PropertyGroup>
    <RepositoryUrl>https://github.com/your-org/sms-gateway</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>


  <!-- Build configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>$(DefineConstants);RELEASE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- Common package versions -->
  <PropertyGroup>
    <MicrosoftAspNetCoreVersion>9.0.0</MicrosoftAspNetCoreVersion>
    <MicrosoftEntityFrameworkCoreVersion>9.0.0</MicrosoftEntityFrameworkCoreVersion>
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
    <SystemTextJsonVersion>9.0.0</SystemTextJsonVersion>
    <FinbuckleMultiTenantVersion>7.0.2</FinbuckleMultiTenantVersion>
    <HangfireVersion>1.8.14</HangfireVersion>
    <StackExchangeRedisVersion>2.8.16</StackExchangeRedisVersion>
    <SerilogVersion>8.0.2</SerilogVersion>
    <SwashbuckleVersion>7.2.0</SwashbuckleVersion>
    <FluentValidationVersion>11.3.0</FluentValidationVersion>
    <BCryptVersion>4.0.3</BCryptVersion>
    <DotLiquidVersion>2.2.692</DotLiquidVersion>
    <JwtVersion>8.2.1</JwtVersion>
    <MudBlazorVersion>8.0.0</MudBlazorVersion>
    <BlazoredToastVersion>4.2.1</BlazoredToastVersion>
    <BlazoredModalVersion>7.3.1</BlazoredModalVersion>
  </PropertyGroup>

  <!-- Static analysis -->
  <PropertyGroup>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <AnalysisMode>Default</AnalysisMode>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)sms-gateway.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <!-- Source generators -->
  <PropertyGroup>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)\Generated</CompilerGeneratedFilesOutputPath>
  </PropertyGroup>

  <!-- Deterministic builds -->
  <PropertyGroup Condition="'$(GITHUB_ACTIONS)' == 'true' OR '$(TF_BUILD)' == 'true'">
    <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
    <Deterministic>true</Deterministic>
  </PropertyGroup>

</Project>
